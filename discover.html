<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>发现 - 播客App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="mobile-container">
        <!-- iOS 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <span>100%</span>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>

        <!-- 导航栏 -->
        <div class="nav-bar">
            <div class="nav-title">发现</div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 分类标签 -->
            <div class="px-4 py-3">
                <div class="flex space-x-2 overflow-x-auto">
                    <button class="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap">全部</button>
                    <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap">科技</button>
                    <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap">商业</button>
                    <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap">教育</button>
                    <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap">娱乐</button>
                    <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap">健康</button>
                </div>
            </div>

            <!-- 精选推荐横幅 -->
            <div class="px-4 mb-6">
                <div class="bg-gradient-to-r from-purple-500 to-pink-500 rounded-xl p-4 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="font-semibold text-lg">本周精选</h3>
                            <p class="text-sm opacity-90 mt-1">探索最受欢迎的播客内容</p>
                        </div>
                        <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center">
                            <i class="fas fa-star text-2xl"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 热门排行榜 -->
            <div class="px-4 mb-6">
                <div class="flex items-center justify-between mb-3">
                    <h2 class="text-lg font-semibold text-gray-900">热门排行榜</h2>
                    <button class="text-blue-500 text-sm">查看全部</button>
                </div>
                <div class="space-y-3">
                    <!-- 排行榜项目1 -->
                    <div class="bg-white rounded-xl p-4 shadow-sm">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-red-500 rounded-full flex items-center justify-center">
                                <span class="text-white font-bold text-sm">1</span>
                            </div>
                            <img src="https://images.unsplash.com/photo-1590602847861-f357a9332bbc?w=50&h=50&fit=crop&crop=center" 
                                 alt="播客封面" class="w-12 h-12 rounded-lg">
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-900 text-sm">创业故事汇</h3>
                                <p class="text-xs text-gray-500">商业 · 125集</p>
                            </div>
                            <div class="text-right">
                                <div class="text-xs text-gray-400">
                                    <i class="fas fa-arrow-up text-red-500 mr-1"></i>+15%
                                </div>
                                <div class="text-xs text-gray-500 mt-1">12.5万</div>
                            </div>
                        </div>
                    </div>

                    <!-- 排行榜项目2 -->
                    <div class="bg-white rounded-xl p-4 shadow-sm">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-orange-500 rounded-full flex items-center justify-center">
                                <span class="text-white font-bold text-sm">2</span>
                            </div>
                            <img src="https://images.unsplash.com/photo-1478737270239-2f02b77fc618?w=50&h=50&fit=crop&crop=center" 
                                 alt="播客封面" class="w-12 h-12 rounded-lg">
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-900 text-sm">科技聊天室</h3>
                                <p class="text-xs text-gray-500">科技 · 89集</p>
                            </div>
                            <div class="text-right">
                                <div class="text-xs text-gray-400">
                                    <i class="fas fa-arrow-up text-green-500 mr-1"></i>+8%
                                </div>
                                <div class="text-xs text-gray-500 mt-1">9.8万</div>
                            </div>
                        </div>
                    </div>

                    <!-- 排行榜项目3 -->
                    <div class="bg-white rounded-xl p-4 shadow-sm">
                        <div class="flex items-center space-x-3">
                            <div class="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center">
                                <span class="text-white font-bold text-sm">3</span>
                            </div>
                            <img src="https://images.unsplash.com/photo-1516280440614-37939bbacd81?w=50&h=50&fit=crop&crop=center" 
                                 alt="播客封面" class="w-12 h-12 rounded-lg">
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-900 text-sm">深度思考</h3>
                                <p class="text-xs text-gray-500">教育 · 156集</p>
                            </div>
                            <div class="text-right">
                                <div class="text-xs text-gray-400">
                                    <i class="fas fa-arrow-down text-red-500 mr-1"></i>-3%
                                </div>
                                <div class="text-xs text-gray-500 mt-1">8.3万</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 分类浏览 -->
            <div class="px-4 mb-6">
                <h2 class="text-lg font-semibold text-gray-900 mb-3">分类浏览</h2>
                <div class="grid grid-cols-2 gap-3">
                    <!-- 科技分类 -->
                    <div class="bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl p-4 text-white">
                        <div class="flex items-center justify-between mb-2">
                            <i class="fas fa-laptop text-2xl"></i>
                            <span class="text-xs bg-white bg-opacity-20 px-2 py-1 rounded-full">128个</span>
                        </div>
                        <h3 class="font-semibold">科技</h3>
                        <p class="text-xs opacity-90">前沿科技资讯</p>
                    </div>

                    <!-- 商业分类 -->
                    <div class="bg-gradient-to-br from-green-500 to-green-600 rounded-xl p-4 text-white">
                        <div class="flex items-center justify-between mb-2">
                            <i class="fas fa-chart-line text-2xl"></i>
                            <span class="text-xs bg-white bg-opacity-20 px-2 py-1 rounded-full">95个</span>
                        </div>
                        <h3 class="font-semibold">商业</h3>
                        <p class="text-xs opacity-90">商业洞察分析</p>
                    </div>

                    <!-- 教育分类 -->
                    <div class="bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl p-4 text-white">
                        <div class="flex items-center justify-between mb-2">
                            <i class="fas fa-graduation-cap text-2xl"></i>
                            <span class="text-xs bg-white bg-opacity-20 px-2 py-1 rounded-full">76个</span>
                        </div>
                        <h3 class="font-semibold">教育</h3>
                        <p class="text-xs opacity-90">知识学习成长</p>
                    </div>

                    <!-- 娱乐分类 -->
                    <div class="bg-gradient-to-br from-pink-500 to-pink-600 rounded-xl p-4 text-white">
                        <div class="flex items-center justify-between mb-2">
                            <i class="fas fa-music text-2xl"></i>
                            <span class="text-xs bg-white bg-opacity-20 px-2 py-1 rounded-full">203个</span>
                        </div>
                        <h3 class="font-semibold">娱乐</h3>
                        <p class="text-xs opacity-90">轻松娱乐内容</p>
                    </div>
                </div>
            </div>

            <!-- 新播客推荐 -->
            <div class="px-4 mb-6">
                <div class="flex items-center justify-between mb-3">
                    <h2 class="text-lg font-semibold text-gray-900">新播客推荐</h2>
                    <button class="text-blue-500 text-sm">查看全部</button>
                </div>
                <div class="space-y-3">
                    <!-- 新播客1 -->
                    <div class="bg-white rounded-xl p-4 shadow-sm">
                        <div class="flex items-start space-x-3">
                            <img src="https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=60&h=60&fit=crop&crop=center" 
                                 alt="播客封面" class="w-15 h-15 rounded-lg">
                            <div class="flex-1">
                                <div class="flex items-center mb-1">
                                    <h3 class="font-medium text-gray-900 text-sm">理财有道</h3>
                                    <span class="ml-2 bg-red-500 text-white text-xs px-2 py-0.5 rounded-full">新</span>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">专业理财知识分享</p>
                                <div class="flex items-center mt-2 space-x-4">
                                    <span class="text-xs text-gray-400">
                                        <i class="fas fa-calendar mr-1"></i>每周更新
                                    </span>
                                    <span class="text-xs text-gray-400">
                                        <i class="fas fa-clock mr-1"></i>30-45分钟
                                    </span>
                                </div>
                            </div>
                            <button class="btn-secondary text-xs px-3 py-1">试听</button>
                        </div>
                    </div>

                    <!-- 新播客2 -->
                    <div class="bg-white rounded-xl p-4 shadow-sm">
                        <div class="flex items-start space-x-3">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=center" 
                                 alt="播客封面" class="w-15 h-15 rounded-lg">
                            <div class="flex-1">
                                <div class="flex items-center mb-1">
                                    <h3 class="font-medium text-gray-900 text-sm">职场进阶</h3>
                                    <span class="ml-2 bg-red-500 text-white text-xs px-2 py-0.5 rounded-full">新</span>
                                </div>
                                <p class="text-xs text-gray-500 mt-1">职场技能提升指南</p>
                                <div class="flex items-center mt-2 space-x-4">
                                    <span class="text-xs text-gray-400">
                                        <i class="fas fa-calendar mr-1"></i>每周更新
                                    </span>
                                    <span class="text-xs text-gray-400">
                                        <i class="fas fa-clock mr-1"></i>25-40分钟
                                    </span>
                                </div>
                            </div>
                            <button class="btn-secondary text-xs px-3 py-1">试听</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部标签栏 -->
        <div class="tab-bar">
            <div class="tab-item">
                <i class="fas fa-home"></i>
                <span>首页</span>
            </div>
            <div class="tab-item active">
                <i class="fas fa-compass"></i>
                <span>发现</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-bookmark"></i>
                <span>订阅</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </div>
        </div>
    </div>
</body>
</html>
