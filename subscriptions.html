<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订阅 - 播客App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="mobile-container">
        <!-- iOS 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <span>100%</span>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>

        <!-- 导航栏 -->
        <div class="nav-bar">
            <div class="nav-title">我的订阅</div>
            <button class="absolute right-4 top-1/2 transform -translate-y-1/2">
                <i class="fas fa-search text-lg text-gray-600"></i>
            </button>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 筛选标签 -->
            <div class="px-4 py-3">
                <div class="flex space-x-2">
                    <button class="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-medium">全部</button>
                    <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium">有更新</button>
                    <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium">已下载</button>
                </div>
            </div>

            <!-- 统计信息 -->
            <div class="px-4 mb-4">
                <div class="bg-gradient-to-r from-blue-500 to-purple-600 rounded-xl p-4 text-white">
                    <div class="flex items-center justify-between">
                        <div>
                            <h3 class="text-lg font-semibold">订阅统计</h3>
                            <p class="text-sm opacity-90">共订阅 12 个播客</p>
                        </div>
                        <div class="text-right">
                            <div class="text-2xl font-bold">5</div>
                            <div class="text-sm opacity-90">有更新</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 最新更新 -->
            <div class="px-4 mb-6">
                <div class="flex items-center justify-between mb-3">
                    <h2 class="text-lg font-semibold text-gray-900">最新更新</h2>
                    <button class="text-blue-500 text-sm">全部标记已读</button>
                </div>
                <div class="space-y-3">
                    <!-- 更新项目1 -->
                    <div class="bg-white rounded-xl p-4 shadow-sm border-l-4 border-blue-500">
                        <div class="flex items-start space-x-3">
                            <div class="relative">
                                <img src="https://images.unsplash.com/photo-1478737270239-2f02b77fc618?w=60&h=60&fit=crop&crop=center" 
                                     alt="播客封面" class="w-15 h-15 rounded-lg">
                                <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                                    <span class="text-white text-xs font-bold">3</span>
                                </div>
                            </div>
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-900 text-sm">科技聊天室</h3>
                                <p class="text-xs text-gray-500 mt-1">3个新单集 · 2小时前</p>
                                <p class="text-xs text-blue-600 mt-1 font-medium">第44期：量子计算的突破</p>
                            </div>
                            <button class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-play text-white text-xs"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 更新项目2 -->
                    <div class="bg-white rounded-xl p-4 shadow-sm border-l-4 border-green-500">
                        <div class="flex items-start space-x-3">
                            <div class="relative">
                                <img src="https://images.unsplash.com/photo-1590602847861-f357a9332bbc?w=60&h=60&fit=crop&crop=center" 
                                     alt="播客封面" class="w-15 h-15 rounded-lg">
                                <div class="absolute -top-1 -right-1 w-4 h-4 bg-red-500 rounded-full flex items-center justify-center">
                                    <span class="text-white text-xs font-bold">1</span>
                                </div>
                            </div>
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-900 text-sm">创业故事汇</h3>
                                <p class="text-xs text-gray-500 mt-1">1个新单集 · 5小时前</p>
                                <p class="text-xs text-green-600 mt-1 font-medium">从0到1的创业心路历程</p>
                            </div>
                            <button class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-play text-white text-xs"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 我的播客 -->
            <div class="px-4 mb-6">
                <div class="flex items-center justify-between mb-3">
                    <h2 class="text-lg font-semibold text-gray-900">我的播客</h2>
                    <button class="text-blue-500 text-sm">管理</button>
                </div>
                <div class="space-y-3">
                    <!-- 播客项目1 -->
                    <div class="bg-white rounded-xl p-4 shadow-sm">
                        <div class="flex items-center space-x-3">
                            <img src="https://images.unsplash.com/photo-1478737270239-2f02b77fc618?w=50&h=50&fit=crop&crop=center" 
                                 alt="播客封面" class="w-12 h-12 rounded-lg">
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-900 text-sm">科技聊天室</h3>
                                <p class="text-xs text-gray-500">89集 · 每周更新</p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs text-blue-600 bg-blue-50 px-2 py-0.5 rounded-full">有更新</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-download text-gray-600 text-xs"></i>
                                </button>
                                <button class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-bell text-gray-600 text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 播客项目2 -->
                    <div class="bg-white rounded-xl p-4 shadow-sm">
                        <div class="flex items-center space-x-3">
                            <img src="https://images.unsplash.com/photo-1590602847861-f357a9332bbc?w=50&h=50&fit=crop&crop=center" 
                                 alt="播客封面" class="w-12 h-12 rounded-lg">
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-900 text-sm">创业故事汇</h3>
                                <p class="text-xs text-gray-500">125集 · 每周更新</p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs text-green-600 bg-green-50 px-2 py-0.5 rounded-full">有更新</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-download text-gray-600 text-xs"></i>
                                </button>
                                <button class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-bell text-white text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 播客项目3 -->
                    <div class="bg-white rounded-xl p-4 shadow-sm">
                        <div class="flex items-center space-x-3">
                            <img src="https://images.unsplash.com/photo-1516280440614-37939bbacd81?w=50&h=50&fit=crop&crop=center" 
                                 alt="播客封面" class="w-12 h-12 rounded-lg">
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-900 text-sm">深度思考</h3>
                                <p class="text-xs text-gray-500">156集 · 每周更新</p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs text-gray-500">暂无更新</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-download text-gray-600 text-xs"></i>
                                </button>
                                <button class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-bell-slash text-gray-400 text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 播客项目4 -->
                    <div class="bg-white rounded-xl p-4 shadow-sm">
                        <div class="flex items-center space-x-3">
                            <img src="https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=50&h=50&fit=crop&crop=center" 
                                 alt="播客封面" class="w-12 h-12 rounded-lg">
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-900 text-sm">理财有道</h3>
                                <p class="text-xs text-gray-500">32集 · 每周更新</p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs text-orange-600 bg-orange-50 px-2 py-0.5 rounded-full">有更新</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-download text-white text-xs"></i>
                                </button>
                                <button class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-bell text-white text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 播客项目5 -->
                    <div class="bg-white rounded-xl p-4 shadow-sm">
                        <div class="flex items-center space-x-3">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=50&h=50&fit=crop&crop=center" 
                                 alt="播客封面" class="w-12 h-12 rounded-lg">
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-900 text-sm">职场进阶</h3>
                                <p class="text-xs text-gray-500">18集 · 每周更新</p>
                                <div class="flex items-center mt-1">
                                    <span class="text-xs text-purple-600 bg-purple-50 px-2 py-0.5 rounded-full">有更新</span>
                                </div>
                            </div>
                            <div class="flex items-center space-x-2">
                                <button class="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center">
                                    <i class="fas fa-download text-gray-600 text-xs"></i>
                                </button>
                                <button class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                    <i class="fas fa-bell text-white text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 发现更多 -->
            <div class="px-4 mb-6">
                <div class="bg-gray-50 rounded-xl p-4 text-center">
                    <i class="fas fa-plus-circle text-3xl text-blue-500 mb-2"></i>
                    <h3 class="font-semibold text-gray-900 mb-1">发现更多播客</h3>
                    <p class="text-sm text-gray-500 mb-3">探索更多有趣的播客内容</p>
                    <button class="btn-primary text-sm px-6 py-2">去发现</button>
                </div>
            </div>
        </div>

        <!-- 底部标签栏 -->
        <div class="tab-bar">
            <div class="tab-item">
                <i class="fas fa-home"></i>
                <span>首页</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-compass"></i>
                <span>发现</span>
            </div>
            <div class="tab-item active">
                <i class="fas fa-bookmark"></i>
                <span>订阅</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </div>
        </div>
    </div>
</body>
</html>
