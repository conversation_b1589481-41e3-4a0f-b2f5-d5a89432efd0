/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #007AFF;
    --secondary-color: #5856D6;
    --accent-color: #FF3B30;
    --success-color: #34C759;
    --warning-color: #FF9500;
    --text-primary: #1D1D1F;
    --text-secondary: #86868B;
    --background-primary: #FFFFFF;
    --background-secondary: #F5F5F7;
    --background-tertiary: #FAFAFA;
    --border-color: #D2D2D7;
    --shadow-light: 0 2px 10px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 0, 0, 0.15);
    --shadow-heavy: 0 8px 40px rgba(0, 0, 0, 0.2);
    --border-radius: 12px;
    --border-radius-large: 20px;
    --transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'SF Pro Display', '<PERSON><PERSON><PERSON> UI', <PERSON><PERSON>, sans-serif;
    line-height: 1.6;
    color: var(--text-primary);
    background-color: var(--background-primary);
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Navigation */
.navbar {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-bottom: 1px solid var(--border-color);
    z-index: 1000;
    transition: var(--transition);
}

.nav-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 60px;
}

.nav-logo .logo-text {
    font-size: 24px;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.nav-menu {
    display: flex;
    list-style: none;
    align-items: center;
    gap: 32px;
}

.nav-menu a {
    text-decoration: none;
    color: var(--text-primary);
    font-weight: 500;
    transition: var(--transition);
    position: relative;
}

.nav-menu a:hover {
    color: var(--primary-color);
}

.nav-menu a::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 0;
    width: 0;
    height: 2px;
    background: var(--primary-color);
    transition: var(--transition);
}

.nav-menu a:hover::after {
    width: 100%;
}

.cta-button {
    background: var(--primary-color);
    color: white !important;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: 600;
    transition: var(--transition);
}

.cta-button:hover {
    background: #0056CC;
    transform: translateY(-1px);
}

.hamburger {
    display: none;
    flex-direction: column;
    cursor: pointer;
    gap: 4px;
}

.hamburger span {
    width: 24px;
    height: 2px;
    background: var(--text-primary);
    transition: var(--transition);
}

/* Hero Section */
.hero {
    position: relative;
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
}

.gradient-orb {
    position: absolute;
    border-radius: 50%;
    filter: blur(60px);
    opacity: 0.7;
    animation: float 6s ease-in-out infinite;
}

.orb-1 {
    width: 300px;
    height: 300px;
    background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
    top: 20%;
    left: 10%;
    animation-delay: 0s;
}

.orb-2 {
    width: 200px;
    height: 200px;
    background: linear-gradient(45deg, #A8E6CF, #FFD93D);
    top: 60%;
    right: 20%;
    animation-delay: 2s;
}

.orb-3 {
    width: 250px;
    height: 250px;
    background: linear-gradient(45deg, #FF8A80, #EA80FC);
    bottom: 20%;
    left: 60%;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-20px) rotate(120deg); }
    66% { transform: translateY(10px) rotate(240deg); }
}

.hero-content {
    position: relative;
    z-index: 2;
    text-align: center;
    color: white;
    max-width: 800px;
    padding: 0 20px;
}

.hero-title {
    font-size: clamp(3rem, 8vw, 6rem);
    font-weight: 700;
    line-height: 1.1;
    margin-bottom: 24px;
    opacity: 0;
    animation: slideUp 1s ease-out 0.5s forwards;
}

.title-line {
    display: block;
}

.hero-subtitle {
    font-size: clamp(1.1rem, 2.5vw, 1.5rem);
    font-weight: 400;
    margin-bottom: 40px;
    opacity: 0.9;
    opacity: 0;
    animation: slideUp 1s ease-out 0.7s forwards;
}

.hero-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-bottom: 60px;
    flex-wrap: wrap;
    opacity: 0;
    animation: slideUp 1s ease-out 0.9s forwards;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 24px;
    border-radius: var(--border-radius);
    text-decoration: none;
    font-weight: 600;
    transition: var(--transition);
    border: none;
    cursor: pointer;
    font-size: 16px;
    min-width: 140px;
}

.btn-primary {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-primary:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
    box-shadow: var(--shadow-medium);
}

.btn-secondary {
    background: transparent;
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.5);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

.btn.large {
    padding: 16px 32px;
    font-size: 18px;
    min-width: 160px;
}

.hero-stats {
    display: flex;
    justify-content: center;
    gap: 60px;
    flex-wrap: wrap;
    opacity: 0;
    animation: slideUp 1s ease-out 1.1s forwards;
}

.stat {
    text-align: center;
}

.stat-number {
    display: block;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 8px;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.8;
    font-weight: 500;
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Section Styles */
.section-header {
    text-align: center;
    margin-bottom: 80px;
}

.section-title {
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    font-weight: 700;
    margin-bottom: 20px;
    background: linear-gradient(135deg, var(--text-primary), var(--text-secondary));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.section-subtitle {
    font-size: 1.25rem;
    color: var(--text-secondary);
    max-width: 600px;
    margin: 0 auto;
    line-height: 1.5;
}

/* Models Section */
.models-section {
    padding: 120px 0;
    background: var(--background-secondary);
}

.models-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 40px;
    max-width: 1000px;
    margin: 0 auto;
}

.model-card {
    background: var(--background-primary);
    border-radius: var(--border-radius-large);
    padding: 40px;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    position: relative;
    overflow: hidden;
}

.model-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--primary-color), var(--secondary-color));
}

.model-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-heavy);
}

.model-icon {
    margin-bottom: 24px;
}

.icon-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.opus-gradient {
    background: linear-gradient(135deg, #FF6B6B, #4ECDC4);
}

.sonnet-gradient {
    background: linear-gradient(135deg, #667eea, #764ba2);
}

.icon-circle svg {
    width: 24px;
    height: 24px;
}

.model-name {
    font-size: 1.75rem;
    font-weight: 700;
    margin-bottom: 12px;
    color: var(--text-primary);
}

.model-description {
    color: var(--text-secondary);
    margin-bottom: 24px;
    line-height: 1.6;
}

.model-features {
    list-style: none;
    margin-bottom: 32px;
}

.model-features li {
    padding: 8px 0;
    position: relative;
    padding-left: 24px;
    color: var(--text-primary);
}

.model-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
}

.model-pricing {
    display: flex;
    align-items: baseline;
    gap: 8px;
}

.price {
    font-size: 2rem;
    font-weight: 700;
    color: var(--primary-color);
}

.price-unit {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Features Section */
.features-section {
    padding: 120px 0;
    background: var(--background-primary);
}

.features-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 32px;
}

.feature-card {
    text-align: center;
    padding: 40px 24px;
    border-radius: var(--border-radius);
    transition: var(--transition);
    background: var(--background-tertiary);
    border: 1px solid var(--border-color);
}

.feature-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
    background: var(--background-primary);
}

.feature-icon {
    width: 60px;
    height: 60px;
    margin: 0 auto 24px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
}

.feature-icon svg {
    width: 24px;
    height: 24px;
}

.feature-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.feature-description {
    color: var(--text-secondary);
    line-height: 1.6;
}

/* Performance Section */
.performance-section {
    padding: 120px 0;
    background: var(--background-secondary);
}

.performance-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 60px;
    align-items: start;
}

.performance-chart {
    background: var(--background-primary);
    border-radius: var(--border-radius-large);
    padding: 40px;
    box-shadow: var(--shadow-light);
}

.chart-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 32px;
    text-align: center;
    color: var(--text-primary);
}

.chart-container {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.chart-bar {
    display: flex;
    align-items: center;
    gap: 16px;
}

.bar-label {
    min-width: 120px;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.9rem;
}

.bar-fill {
    flex: 1;
    height: 32px;
    border-radius: 16px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding-right: 12px;
    transition: var(--transition);
    animation: fillBar 1.5s ease-out forwards;
    transform-origin: left;
    transform: scaleX(0);
}

.opus-bar {
    background: linear-gradient(90deg, #FF6B6B, #4ECDC4);
}

.sonnet-bar {
    background: linear-gradient(90deg, #667eea, #764ba2);
}

.competitor-bar {
    background: linear-gradient(90deg, #D2D2D7, #86868B);
}

.bar-value {
    color: white;
    font-weight: 600;
    font-size: 0.9rem;
}

@keyframes fillBar {
    to {
        transform: scaleX(1);
    }
}

.performance-highlights {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.highlight-card {
    background: var(--background-primary);
    border-radius: var(--border-radius);
    padding: 32px;
    text-align: center;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
}

.highlight-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
}

.highlight-number {
    font-size: 3rem;
    font-weight: 700;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 8px;
}

.highlight-label {
    font-size: 1.1rem;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 8px;
}

.highlight-description {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

/* Use Cases Section */
.use-cases-section {
    padding: 120px 0;
    background: var(--background-primary);
}

.use-cases-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 32px;
}

.use-case-card {
    background: var(--background-tertiary);
    border-radius: var(--border-radius);
    padding: 32px;
    transition: var(--transition);
    border: 1px solid var(--border-color);
}

.use-case-card:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-medium);
    background: var(--background-primary);
}

.use-case-icon {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    margin-bottom: 20px;
}

.use-case-icon svg {
    width: 24px;
    height: 24px;
}

.use-case-title {
    font-size: 1.25rem;
    font-weight: 600;
    margin-bottom: 12px;
    color: var(--text-primary);
}

.use-case-description {
    color: var(--text-secondary);
    margin-bottom: 20px;
    line-height: 1.6;
}

.use-case-companies {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.use-case-companies span {
    background: var(--background-secondary);
    color: var(--text-secondary);
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 0.8rem;
    font-weight: 500;
}

/* Pricing Section */
.pricing-section {
    padding: 120px 0;
    background: var(--background-secondary);
}

.pricing-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
    gap: 32px;
    max-width: 1000px;
    margin: 0 auto;
}

.pricing-card {
    background: var(--background-primary);
    border-radius: var(--border-radius-large);
    padding: 40px;
    box-shadow: var(--shadow-light);
    transition: var(--transition);
    position: relative;
    border: 2px solid transparent;
}

.pricing-card:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-heavy);
}

.pricing-card.featured {
    border-color: var(--primary-color);
    transform: scale(1.05);
}

.pricing-card.featured:hover {
    transform: scale(1.05) translateY(-8px);
}

.pricing-badge {
    position: absolute;
    top: -12px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--primary-color);
    color: white;
    padding: 6px 20px;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.pricing-header {
    text-align: center;
    margin-bottom: 32px;
}

.pricing-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 16px;
    color: var(--text-primary);
}

.pricing-price {
    margin-bottom: 12px;
}

.price-amount {
    font-size: 3rem;
    font-weight: 700;
    color: var(--primary-color);
}

.price-period {
    font-size: 1rem;
    color: var(--text-secondary);
    font-weight: 500;
}

.pricing-description {
    color: var(--text-secondary);
    line-height: 1.5;
}

.pricing-features {
    list-style: none;
    margin-bottom: 32px;
}

.pricing-features li {
    padding: 12px 0;
    position: relative;
    padding-left: 28px;
    color: var(--text-primary);
    border-bottom: 1px solid var(--border-color);
}

.pricing-features li:last-child {
    border-bottom: none;
}

.pricing-features li::before {
    content: '✓';
    position: absolute;
    left: 0;
    color: var(--success-color);
    font-weight: bold;
    font-size: 1.1rem;
}

.pricing-button {
    display: block;
    width: 100%;
    padding: 16px;
    background: var(--primary-color);
    color: white;
    text-decoration: none;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-align: center;
    transition: var(--transition);
    border: none;
    cursor: pointer;
}

.pricing-button:hover {
    background: #0056CC;
    transform: translateY(-2px);
}

.pricing-card.free .pricing-button {
    background: var(--background-secondary);
    color: var(--text-primary);
    border: 2px solid var(--border-color);
}

.pricing-card.free .pricing-button:hover {
    background: var(--background-tertiary);
    border-color: var(--primary-color);
}

.pricing-card.enterprise .pricing-button {
    background: var(--text-primary);
}

.pricing-card.enterprise .pricing-button:hover {
    background: #000;
}

/* CTA Section */
.cta-section {
    padding: 120px 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    text-align: center;
}

.cta-content {
    max-width: 600px;
    margin: 0 auto;
}

.cta-title {
    font-size: clamp(2.5rem, 5vw, 3.5rem);
    font-weight: 700;
    margin-bottom: 20px;
}

.cta-subtitle {
    font-size: 1.25rem;
    margin-bottom: 40px;
    opacity: 0.9;
}

.cta-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    flex-wrap: wrap;
}

.cta-section .btn-primary {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.cta-section .btn-secondary {
    background: transparent;
    border: 2px solid rgba(255, 255, 255, 0.5);
}

/* Footer */
.footer {
    background: var(--text-primary);
    color: white;
    padding: 80px 0 40px;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 40px;
    margin-bottom: 40px;
}

.footer-title {
    font-size: 1.1rem;
    font-weight: 600;
    margin-bottom: 20px;
    color: white;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 12px;
}

.footer-links a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    transition: var(--transition);
}

.footer-links a:hover {
    color: white;
}

.footer-bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding-top: 40px;
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    flex-wrap: wrap;
    gap: 20px;
}

.footer-legal {
    display: flex;
    gap: 24px;
}

.footer-legal a {
    color: rgba(255, 255, 255, 0.7);
    text-decoration: none;
    font-size: 0.9rem;
    transition: var(--transition);
}

.footer-legal a:hover {
    color: white;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .performance-grid {
        grid-template-columns: 1fr;
        gap: 40px;
    }

    .models-grid {
        grid-template-columns: 1fr;
        max-width: 600px;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        position: fixed;
        top: 60px;
        left: -100%;
        width: 100%;
        height: calc(100vh - 60px);
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        -webkit-backdrop-filter: blur(20px);
        flex-direction: column;
        justify-content: flex-start;
        align-items: center;
        padding-top: 60px;
        transition: var(--transition);
        z-index: 999;
    }

    .nav-menu.active {
        left: 0;
    }

    .hamburger {
        display: flex;
    }

    .hamburger.active span:nth-child(1) {
        transform: rotate(45deg) translate(5px, 5px);
    }

    .hamburger.active span:nth-child(2) {
        opacity: 0;
    }

    .hamburger.active span:nth-child(3) {
        transform: rotate(-45deg) translate(7px, -6px);
    }

    .hero-buttons {
        flex-direction: column;
        align-items: center;
    }

    .hero-stats {
        gap: 40px;
    }

    .features-grid {
        grid-template-columns: 1fr;
    }

    .use-cases-grid {
        grid-template-columns: 1fr;
    }

    .pricing-grid {
        grid-template-columns: 1fr;
        max-width: 400px;
    }

    .pricing-card.featured {
        transform: none;
    }

    .pricing-card.featured:hover {
        transform: translateY(-8px);
    }

    .cta-buttons {
        flex-direction: column;
        align-items: center;
    }

    .footer-bottom {
        flex-direction: column;
        text-align: center;
    }

    .footer-legal {
        justify-content: center;
    }

    .container {
        padding: 0 16px;
    }

    .section-header {
        margin-bottom: 60px;
    }

    .models-section,
    .features-section,
    .performance-section,
    .use-cases-section,
    .pricing-section,
    .cta-section {
        padding: 80px 0;
    }
}

@media (max-width: 480px) {
    .nav-container {
        padding: 0 16px;
    }

    .hero-stats {
        flex-direction: column;
        gap: 24px;
    }

    .chart-bar {
        flex-direction: column;
        align-items: flex-start;
        gap: 8px;
    }

    .bar-label {
        min-width: auto;
    }

    .bar-fill {
        width: 100%;
    }

    .performance-highlights {
        gap: 16px;
    }

    .highlight-card {
        padding: 24px;
    }

    .highlight-number {
        font-size: 2.5rem;
    }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .gradient-orb {
        animation: none;
    }
}

/* Focus styles for accessibility */
.btn:focus,
.pricing-button:focus,
.nav-menu a:focus {
    outline: 2px solid var(--primary-color);
    outline-offset: 2px;
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Loading animation for performance bars */
.chart-bar .bar-fill {
    animation-delay: calc(var(--index, 0) * 0.2s);
}

/* Intersection Observer animations */
.fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.6s ease-out, transform 0.6s ease-out;
}

.fade-in.visible {
    opacity: 1;
    transform: translateY(0);
}

/* Fix backdrop-filter for Safari */
.navbar,
.nav-menu,
.btn-primary,
.cta-section .btn-primary {
    -webkit-backdrop-filter: blur(20px);
    backdrop-filter: blur(20px);
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    :root {
        --text-primary: #FFFFFF;
        --text-secondary: #A1A1A6;
        --background-primary: #000000;
        --background-secondary: #1C1C1E;
        --background-tertiary: #2C2C2E;
        --border-color: #38383A;
    }

    .navbar {
        background: rgba(0, 0, 0, 0.8);
        border-bottom-color: var(--border-color);
    }

    .nav-menu {
        background: rgba(0, 0, 0, 0.95);
    }
}
