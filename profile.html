<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的 - 播客App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="mobile-container">
        <!-- iOS 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <span>100%</span>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>

        <!-- 导航栏 -->
        <div class="nav-bar">
            <div class="nav-title">我的</div>
            <button class="absolute right-4 top-1/2 transform -translate-y-1/2">
                <i class="fas fa-cog text-lg text-gray-600"></i>
            </button>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 用户信息 -->
            <div class="px-4 py-6 bg-white">
                <div class="flex items-center space-x-4 mb-4">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=80&h=80&fit=crop&crop=face" 
                             alt="用户头像" class="w-20 h-20 rounded-full">
                        <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-white"></div>
                    </div>
                    <div class="flex-1">
                        <h2 class="text-xl font-bold text-gray-900">张小明</h2>
                        <p class="text-gray-500 text-sm">播客爱好者</p>
                        <div class="flex items-center mt-2 space-x-4">
                            <span class="text-sm text-gray-600">
                                <i class="fas fa-calendar mr-1"></i>加入 365 天
                            </span>
                            <span class="text-sm text-gray-600">
                                <i class="fas fa-headphones mr-1"></i>听过 128 小时
                            </span>
                        </div>
                    </div>
                </div>
                
                <!-- 统计数据 -->
                <div class="grid grid-cols-3 gap-4">
                    <div class="text-center p-3 bg-blue-50 rounded-lg">
                        <div class="text-2xl font-bold text-blue-600">12</div>
                        <div class="text-xs text-gray-600">订阅播客</div>
                    </div>
                    <div class="text-center p-3 bg-green-50 rounded-lg">
                        <div class="text-2xl font-bold text-green-600">89</div>
                        <div class="text-xs text-gray-600">收藏单集</div>
                    </div>
                    <div class="text-center p-3 bg-purple-50 rounded-lg">
                        <div class="text-2xl font-bold text-purple-600">256</div>
                        <div class="text-xs text-gray-600">播放历史</div>
                    </div>
                </div>
            </div>

            <!-- 快捷功能 -->
            <div class="px-4 py-4">
                <div class="grid grid-cols-4 gap-4">
                    <button class="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm">
                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-2">
                            <i class="fas fa-history text-blue-600"></i>
                        </div>
                        <span class="text-xs text-gray-700">播放历史</span>
                    </button>
                    
                    <button class="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm">
                        <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mb-2">
                            <i class="fas fa-heart text-red-600"></i>
                        </div>
                        <span class="text-xs text-gray-700">我的收藏</span>
                    </button>
                    
                    <button class="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm">
                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-2">
                            <i class="fas fa-download text-green-600"></i>
                        </div>
                        <span class="text-xs text-gray-700">离线下载</span>
                    </button>
                    
                    <button class="flex flex-col items-center p-3 bg-white rounded-xl shadow-sm">
                        <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-2">
                            <i class="fas fa-share-alt text-purple-600"></i>
                        </div>
                        <span class="text-xs text-gray-700">分享推荐</span>
                    </button>
                </div>
            </div>

            <!-- 最近播放 -->
            <div class="px-4 mb-6">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="text-lg font-semibold text-gray-900">最近播放</h3>
                    <button class="text-blue-500 text-sm">查看全部</button>
                </div>
                <div class="space-y-3">
                    <div class="bg-white rounded-xl p-4 shadow-sm">
                        <div class="flex items-center space-x-3">
                            <img src="https://images.unsplash.com/photo-1478737270239-2f02b77fc618?w=50&h=50&fit=crop&crop=center" 
                                 alt="播客封面" class="w-12 h-12 rounded-lg">
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900 text-sm">第42期：AI的未来发展趋势</h4>
                                <p class="text-xs text-gray-500">科技聊天室 · 2小时前</p>
                                <div class="progress-bar mt-2">
                                    <div class="progress-fill" style="width: 35%"></div>
                                </div>
                            </div>
                            <button class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-play text-white text-xs"></i>
                            </button>
                        </div>
                    </div>
                    
                    <div class="bg-white rounded-xl p-4 shadow-sm">
                        <div class="flex items-center space-x-3">
                            <img src="https://images.unsplash.com/photo-1590602847861-f357a9332bbc?w=50&h=50&fit=crop&crop=center" 
                                 alt="播客封面" class="w-12 h-12 rounded-lg">
                            <div class="flex-1">
                                <h4 class="font-medium text-gray-900 text-sm">从0到1的创业心路历程</h4>
                                <p class="text-xs text-gray-500">创业故事汇 · 昨天</p>
                                <div class="progress-bar mt-2">
                                    <div class="progress-fill" style="width: 100%"></div>
                                </div>
                            </div>
                            <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-check text-green-600 text-xs"></i>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 功能菜单 -->
            <div class="px-4 mb-6">
                <div class="bg-white rounded-xl shadow-sm overflow-hidden">
                    <!-- 账户设置 -->
                    <button class="w-full flex items-center justify-between p-4 border-b border-gray-100">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-user text-blue-600"></i>
                            </div>
                            <span class="font-medium text-gray-900">账户设置</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                    
                    <!-- 播放设置 -->
                    <button class="w-full flex items-center justify-between p-4 border-b border-gray-100">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-green-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-play text-green-600"></i>
                            </div>
                            <span class="font-medium text-gray-900">播放设置</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                    
                    <!-- 下载管理 -->
                    <button class="w-full flex items-center justify-between p-4 border-b border-gray-100">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-download text-purple-600"></i>
                            </div>
                            <div class="flex-1 text-left">
                                <div class="font-medium text-gray-900">下载管理</div>
                                <div class="text-xs text-gray-500">已下载 15 个单集</div>
                            </div>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                    
                    <!-- 通知设置 -->
                    <button class="w-full flex items-center justify-between p-4 border-b border-gray-100">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-orange-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-bell text-orange-600"></i>
                            </div>
                            <span class="font-medium text-gray-900">通知设置</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                    
                    <!-- 隐私设置 -->
                    <button class="w-full flex items-center justify-between p-4 border-b border-gray-100">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-shield-alt text-gray-600"></i>
                            </div>
                            <span class="font-medium text-gray-900">隐私设置</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                    
                    <!-- 帮助与反馈 -->
                    <button class="w-full flex items-center justify-between p-4">
                        <div class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-yellow-100 rounded-full flex items-center justify-center">
                                <i class="fas fa-question-circle text-yellow-600"></i>
                            </div>
                            <span class="font-medium text-gray-900">帮助与反馈</span>
                        </div>
                        <i class="fas fa-chevron-right text-gray-400"></i>
                    </button>
                </div>
            </div>

            <!-- 版本信息 -->
            <div class="px-4 mb-6">
                <div class="text-center">
                    <p class="text-sm text-gray-500">播客App v2.1.0</p>
                    <p class="text-xs text-gray-400 mt-1">© 2024 播客科技有限公司</p>
                </div>
            </div>
        </div>

        <!-- 底部标签栏 -->
        <div class="tab-bar">
            <div class="tab-item">
                <i class="fas fa-home"></i>
                <span>首页</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-compass"></i>
                <span>发现</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-bookmark"></i>
                <span>订阅</span>
            </div>
            <div class="tab-item active">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </div>
        </div>
    </div>
</body>
</html>
