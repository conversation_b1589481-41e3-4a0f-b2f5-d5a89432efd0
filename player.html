<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>播放页 - 播客App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="mobile-container">
        <!-- iOS 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <span>100%</span>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>

        <!-- 导航栏 -->
        <div class="nav-bar">
            <button class="absolute left-4 top-1/2 transform -translate-y-1/2">
                <i class="fas fa-chevron-down text-lg text-gray-600"></i>
            </button>
            <div class="nav-title">正在播放</div>
            <button class="absolute right-4 top-1/2 transform -translate-y-1/2">
                <i class="fas fa-ellipsis-h text-lg text-gray-600"></i>
            </button>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content bg-white">
            <!-- 播客封面 -->
            <div class="px-8 py-8 text-center">
                <div class="relative mx-auto w-72 h-72 mb-6">
                    <img src="https://images.unsplash.com/photo-1478737270239-2f02b77fc618?w=300&h=300&fit=crop&crop=center" 
                         alt="播客封面" class="w-full h-full rounded-2xl shadow-2xl">
                    <!-- 播放动画效果 -->
                    <div class="absolute inset-0 rounded-2xl bg-gradient-to-t from-black/20 to-transparent"></div>
                </div>
                
                <!-- 播客信息 -->
                <div class="mb-6">
                    <h1 class="text-xl font-bold text-gray-900 mb-2">第42期：AI的未来发展趋势</h1>
                    <p class="text-gray-600 mb-1">科技聊天室</p>
                    <p class="text-sm text-gray-500">2024年1月15日 · 45分钟</p>
                </div>

                <!-- 进度条 -->
                <div class="mb-6">
                    <div class="progress-bar mb-2">
                        <div class="progress-fill" style="width: 35%"></div>
                    </div>
                    <div class="flex justify-between text-sm text-gray-500">
                        <span>15:42</span>
                        <span>45:00</span>
                    </div>
                </div>

                <!-- 播放控制 -->
                <div class="flex items-center justify-center space-x-8 mb-8">
                    <!-- 上一集 -->
                    <button class="w-12 h-12 flex items-center justify-center">
                        <i class="fas fa-step-backward text-2xl text-gray-600"></i>
                    </button>
                    
                    <!-- 快退15秒 -->
                    <button class="w-12 h-12 flex items-center justify-center">
                        <i class="fas fa-undo text-xl text-gray-600"></i>
                        <span class="absolute text-xs font-bold text-gray-600 mt-6">15</span>
                    </button>
                    
                    <!-- 播放/暂停 -->
                    <button class="play-btn">
                        <i class="fas fa-pause"></i>
                    </button>
                    
                    <!-- 快进30秒 -->
                    <button class="w-12 h-12 flex items-center justify-center">
                        <i class="fas fa-redo text-xl text-gray-600"></i>
                        <span class="absolute text-xs font-bold text-gray-600 mt-6">30</span>
                    </button>
                    
                    <!-- 下一集 -->
                    <button class="w-12 h-12 flex items-center justify-center">
                        <i class="fas fa-step-forward text-2xl text-gray-600"></i>
                    </button>
                </div>

                <!-- 功能按钮 -->
                <div class="flex items-center justify-center space-x-12 mb-6">
                    <!-- 播放速度 -->
                    <button class="flex flex-col items-center">
                        <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mb-1">
                            <span class="text-sm font-semibold text-gray-700">1x</span>
                        </div>
                        <span class="text-xs text-gray-500">速度</span>
                    </button>
                    
                    <!-- 收藏 -->
                    <button class="flex flex-col items-center">
                        <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mb-1">
                            <i class="fas fa-heart text-red-500"></i>
                        </div>
                        <span class="text-xs text-gray-500">收藏</span>
                    </button>
                    
                    <!-- 分享 -->
                    <button class="flex flex-col items-center">
                        <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mb-1">
                            <i class="fas fa-share text-gray-600"></i>
                        </div>
                        <span class="text-xs text-gray-500">分享</span>
                    </button>
                    
                    <!-- 播放列表 -->
                    <button class="flex flex-col items-center">
                        <div class="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mb-1">
                            <i class="fas fa-list text-gray-600"></i>
                        </div>
                        <span class="text-xs text-gray-500">列表</span>
                    </button>
                </div>
            </div>

            <!-- 节目简介 -->
            <div class="px-4 py-4 bg-gray-50">
                <h3 class="font-semibold text-gray-900 mb-3">节目简介</h3>
                <p class="text-sm text-gray-600 leading-relaxed mb-4">
                    在这一期节目中，我们将深入探讨人工智能技术的最新发展趋势，包括大语言模型的突破、AI在各行业的应用前景，以及可能带来的社会影响。我们邀请了业内专家分享他们的见解和预测。
                </p>
                
                <!-- 章节列表 -->
                <h4 class="font-semibold text-gray-900 mb-3">章节</h4>
                <div class="space-y-2">
                    <div class="flex items-center justify-between py-2 px-3 bg-white rounded-lg">
                        <div class="flex items-center space-x-3">
                            <span class="text-sm text-blue-500 font-medium">00:00</span>
                            <span class="text-sm text-gray-900">开场介绍</span>
                        </div>
                        <i class="fas fa-play text-blue-500 text-sm"></i>
                    </div>
                    
                    <div class="flex items-center justify-between py-2 px-3 bg-blue-50 rounded-lg border border-blue-200">
                        <div class="flex items-center space-x-3">
                            <span class="text-sm text-blue-500 font-medium">05:30</span>
                            <span class="text-sm text-gray-900">AI技术现状分析</span>
                        </div>
                        <i class="fas fa-pause text-blue-500 text-sm"></i>
                    </div>
                    
                    <div class="flex items-center justify-between py-2 px-3 bg-white rounded-lg">
                        <div class="flex items-center space-x-3">
                            <span class="text-sm text-gray-500 font-medium">18:45</span>
                            <span class="text-sm text-gray-900">行业应用案例</span>
                        </div>
                        <i class="fas fa-play text-gray-400 text-sm"></i>
                    </div>
                    
                    <div class="flex items-center justify-between py-2 px-3 bg-white rounded-lg">
                        <div class="flex items-center space-x-3">
                            <span class="text-sm text-gray-500 font-medium">32:10</span>
                            <span class="text-sm text-gray-900">未来发展预测</span>
                        </div>
                        <i class="fas fa-play text-gray-400 text-sm"></i>
                    </div>
                </div>
            </div>

            <!-- 相关推荐 -->
            <div class="px-4 py-4">
                <h3 class="font-semibold text-gray-900 mb-3">相关推荐</h3>
                <div class="space-y-3">
                    <div class="flex items-center space-x-3 p-3 bg-white rounded-lg shadow-sm">
                        <img src="https://images.unsplash.com/photo-1590602847861-f357a9332bbc?w=50&h=50&fit=crop&crop=center" 
                             alt="播客封面" class="w-12 h-12 rounded-lg">
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900 text-sm">第43期：区块链技术解析</h4>
                            <p class="text-xs text-gray-500">科技聊天室 · 38分钟</p>
                        </div>
                        <button class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-play text-white text-xs"></i>
                        </button>
                    </div>
                    
                    <div class="flex items-center space-x-3 p-3 bg-white rounded-lg shadow-sm">
                        <img src="https://images.unsplash.com/photo-1516280440614-37939bbacd81?w=50&h=50&fit=crop&crop=center" 
                             alt="播客封面" class="w-12 h-12 rounded-lg">
                        <div class="flex-1">
                            <h4 class="font-medium text-gray-900 text-sm">机器学习入门指南</h4>
                            <p class="text-xs text-gray-500">深度思考 · 42分钟</p>
                        </div>
                        <button class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-play text-white text-xs"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部标签栏 -->
        <div class="tab-bar">
            <div class="tab-item">
                <i class="fas fa-home"></i>
                <span>首页</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-compass"></i>
                <span>发现</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-bookmark"></i>
                <span>订阅</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </div>
        </div>
    </div>
</body>
</html>
