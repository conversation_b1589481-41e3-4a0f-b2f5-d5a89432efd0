<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claude 4 - 下一代AI助手</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=SF+Pro+Display:wght@300;400;500;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <span class="logo-text">Claude 4</span>
            </div>
            <ul class="nav-menu">
                <li><a href="#features">功能特性</a></li>
                <li><a href="#models">模型对比</a></li>
                <li><a href="#performance">性能表现</a></li>
                <li><a href="#pricing">价格方案</a></li>
                <li><a href="#start" class="cta-button">立即开始</a></li>
            </ul>
            <div class="hamburger">
                <span></span>
                <span></span>
                <span></span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <section class="hero">
        <div class="hero-background">
            <div class="gradient-orb orb-1"></div>
            <div class="gradient-orb orb-2"></div>
            <div class="gradient-orb orb-3"></div>
        </div>
        <div class="hero-content">
            <h1 class="hero-title">
                <span class="title-line">Claude 4</span>
                <span class="title-line">重新定义AI助手</span>
            </h1>
            <p class="hero-subtitle">
                世界领先的编程模型，具备扩展思维和工具使用能力。<br>
                为复杂任务提供持续性能，开启AI代理新时代。
            </p>
            <div class="hero-buttons">
                <a href="#start" class="btn btn-primary">免费试用</a>
                <a href="#models" class="btn btn-secondary">了解更多</a>
            </div>
            <div class="hero-stats">
                <div class="stat">
                    <span class="stat-number">72.5%</span>
                    <span class="stat-label">SWE-bench 得分</span>
                </div>
                <div class="stat">
                    <span class="stat-number">2x</span>
                    <span class="stat-label">性能提升</span>
                </div>
                <div class="stat">
                    <span class="stat-number">24/7</span>
                    <span class="stat-label">持续工作</span>
                </div>
            </div>
        </div>
    </section>

    <!-- Models Section -->
    <section id="models" class="models-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">两款强大模型</h2>
                <p class="section-subtitle">Claude Opus 4 和 Sonnet 4，满足不同场景需求</p>
            </div>

            <div class="models-grid">
                <div class="model-card opus">
                    <div class="model-icon">
                        <div class="icon-circle opus-gradient">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M12 2L15.09 8.26L22 9L17 14L18.18 21L12 17.77L5.82 21L7 14L2 9L8.91 8.26L12 2Z" fill="currentColor"/>
                            </svg>
                        </div>
                    </div>
                    <h3 class="model-name">Claude Opus 4</h3>
                    <p class="model-description">世界最佳编程模型，专为复杂长期任务设计</p>
                    <ul class="model-features">
                        <li>SWE-bench 72.5% 得分</li>
                        <li>持续数小时工作能力</li>
                        <li>卓越的代码理解能力</li>
                        <li>高级推理和问题解决</li>
                    </ul>
                    <div class="model-pricing">
                        <span class="price">$15/$75</span>
                        <span class="price-unit">每百万token</span>
                    </div>
                </div>

                <div class="model-card sonnet">
                    <div class="model-icon">
                        <div class="icon-circle sonnet-gradient">
                            <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M13 2L3 14H12L11 22L21 10H12L13 2Z" fill="currentColor"/>
                            </svg>
                        </div>
                    </div>
                    <h3 class="model-name">Claude Sonnet 4</h3>
                    <p class="model-description">性能与效率的完美平衡，适合日常使用</p>
                    <ul class="model-features">
                        <li>SWE-bench 72.7% 得分</li>
                        <li>增强的指令遵循能力</li>
                        <li>优化的响应速度</li>
                        <li>多功能应用场景</li>
                    </ul>
                    <div class="model-pricing">
                        <span class="price">$3/$15</span>
                        <span class="price-unit">每百万token</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">突破性功能</h2>
                <p class="section-subtitle">Claude 4 带来的革命性能力提升</p>
            </div>

            <div class="features-grid">
                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">扩展思维</h3>
                    <p class="feature-description">结合工具使用的深度推理能力，在思考和行动之间无缝切换，提供更准确的解决方案。</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M8 9L12 5L16 9M8 15L12 19L16 15" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">并行工具执行</h3>
                    <p class="feature-description">同时使用多个工具，大幅提升任务处理效率，实现真正的多任务并行处理。</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M19 14C19 18.4183 15.4183 22 11 22C6.58172 22 3 18.4183 3 14C3 9.58172 6.58172 6 11 6C15.4183 6 19 9.58172 19 14Z" stroke="currentColor" stroke-width="2"/>
                            <path d="M21 2L16 7L18 9L23 4L21 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">增强记忆能力</h3>
                    <p class="feature-description">创建和维护记忆文件，存储关键信息，实现长期任务感知和连续性工作。</p>
                </div>

                <div class="feature-card">
                    <div class="feature-icon">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M14 2H6C5.46957 2 4.96086 2.21071 4.58579 2.58579C4.21071 2.96086 4 3.46957 4 4V20C4 20.5304 4.21071 21.0391 4.58579 21.4142C4.96086 21.7893 5.46957 22 6 22H18C18.5304 22 19.0391 21.7893 19.4142 21.4142C19.7893 21.0391 20 20.5304 20 20V8L14 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M14 2V8H20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M16 13H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M16 17H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M10 9H9H8" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">精确指令遵循</h3>
                    <p class="feature-description">减少65%的捷径行为，更准确地理解和执行复杂指令，提供可靠的任务完成。</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Performance Section -->
    <section id="performance" class="performance-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">卓越性能表现</h2>
                <p class="section-subtitle">在各项基准测试中领先业界</p>
            </div>

            <div class="performance-grid">
                <div class="performance-chart">
                    <h3 class="chart-title">SWE-bench 编程基准</h3>
                    <div class="chart-container">
                        <div class="chart-bar">
                            <div class="bar-label">Claude Opus 4</div>
                            <div class="bar-fill opus-bar" data-percentage="72.5">
                                <span class="bar-value">72.5%</span>
                            </div>
                        </div>
                        <div class="chart-bar">
                            <div class="bar-label">Claude Sonnet 4</div>
                            <div class="bar-fill sonnet-bar" data-percentage="72.7">
                                <span class="bar-value">72.7%</span>
                            </div>
                        </div>
                        <div class="chart-bar">
                            <div class="bar-label">GPT-4.1</div>
                            <div class="bar-fill competitor-bar" data-percentage="65.2">
                                <span class="bar-value">65.2%</span>
                            </div>
                        </div>
                        <div class="chart-bar">
                            <div class="bar-label">Gemini 2.5 Pro</div>
                            <div class="bar-fill competitor-bar" data-percentage="58.9">
                                <span class="bar-value">58.9%</span>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="performance-highlights">
                    <div class="highlight-card">
                        <div class="highlight-number">43.2%</div>
                        <div class="highlight-label">Terminal-bench 得分</div>
                        <div class="highlight-description">命令行操作基准测试领先</div>
                    </div>
                    <div class="highlight-card">
                        <div class="highlight-number">87.4%</div>
                        <div class="highlight-label">MMMLU 多语言理解</div>
                        <div class="highlight-description">多语言推理能力突出</div>
                    </div>
                    <div class="highlight-card">
                        <div class="highlight-number">65%</div>
                        <div class="highlight-label">减少捷径行为</div>
                        <div class="highlight-description">更可靠的任务执行</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Use Cases Section -->
    <section class="use-cases-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">应用场景</h2>
                <p class="section-subtitle">Claude 4 在各行业的实际应用</p>
            </div>

            <div class="use-cases-grid">
                <div class="use-case-card">
                    <div class="use-case-icon">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M7 8L3 12L7 16M17 8L21 12L17 16M14 4L10 20" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h3 class="use-case-title">软件开发</h3>
                    <p class="use-case-description">代码生成、调试、重构和复杂项目管理，提升开发效率</p>
                    <div class="use-case-companies">
                        <span>Cursor</span>
                        <span>Replit</span>
                        <span>GitHub</span>
                    </div>
                </div>

                <div class="use-case-card">
                    <div class="use-case-icon">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 6.253V10L16.5 8L12 6.253Z" fill="currentColor"/>
                            <path d="M12 6.253V10L7.5 8L12 6.253Z" fill="currentColor"/>
                            <path d="M12 10V13.747L16.5 12L12 10Z" fill="currentColor"/>
                            <path d="M12 10V13.747L7.5 12L12 10Z" fill="currentColor"/>
                            <path d="M12 13.747V17.5L16.5 15.5L12 13.747Z" fill="currentColor"/>
                            <path d="M12 13.747V17.5L7.5 15.5L12 13.747Z" fill="currentColor"/>
                        </svg>
                    </div>
                    <h3 class="use-case-title">科学研究</h3>
                    <p class="use-case-description">数据分析、文献综述、实验设计和科学发现</p>
                    <div class="use-case-companies">
                        <span>研究机构</span>
                        <span>大学实验室</span>
                    </div>
                </div>

                <div class="use-case-card">
                    <div class="use-case-icon">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M14.828 14.828L21 21M16.5 10.5C16.5 13.8137 13.8137 16.5 10.5 16.5C7.18629 16.5 4.5 13.8137 4.5 10.5C4.5 7.18629 7.18629 4.5 10.5 4.5C13.8137 4.5 16.5 7.18629 16.5 10.5Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h3 class="use-case-title">业务分析</h3>
                    <p class="use-case-description">市场研究、数据洞察、战略规划和决策支持</p>
                    <div class="use-case-companies">
                        <span>咨询公司</span>
                        <span>金融机构</span>
                    </div>
                </div>

                <div class="use-case-card">
                    <div class="use-case-icon">
                        <svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 2L2 7L12 12L22 7L12 2Z" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M2 17L12 22L22 17" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M2 12L12 17L22 12" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <h3 class="use-case-title">内容创作</h3>
                    <p class="use-case-description">写作、编辑、翻译和多媒体内容制作</p>
                    <div class="use-case-companies">
                        <span>媒体公司</span>
                        <span>创意机构</span>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="pricing-section">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">选择适合的方案</h2>
                <p class="section-subtitle">灵活的定价选项，满足不同需求</p>
            </div>

            <div class="pricing-grid">
                <div class="pricing-card free">
                    <div class="pricing-header">
                        <h3 class="pricing-title">免费版</h3>
                        <div class="pricing-price">
                            <span class="price-amount">免费</span>
                        </div>
                        <p class="pricing-description">体验 Claude Sonnet 4 的基础功能</p>
                    </div>
                    <ul class="pricing-features">
                        <li>Claude Sonnet 4 访问</li>
                        <li>基础对话功能</li>
                        <li>有限使用次数</li>
                        <li>社区支持</li>
                    </ul>
                    <a href="#start" class="pricing-button">立即开始</a>
                </div>

                <div class="pricing-card pro featured">
                    <div class="pricing-badge">推荐</div>
                    <div class="pricing-header">
                        <h3 class="pricing-title">专业版</h3>
                        <div class="pricing-price">
                            <span class="price-amount">$20</span>
                            <span class="price-period">/月</span>
                        </div>
                        <p class="pricing-description">适合个人用户和小团队</p>
                    </div>
                    <ul class="pricing-features">
                        <li>Claude Opus 4 和 Sonnet 4</li>
                        <li>扩展思维功能</li>
                        <li>优先访问权限</li>
                        <li>更高使用限额</li>
                        <li>邮件支持</li>
                    </ul>
                    <a href="#start" class="pricing-button">选择专业版</a>
                </div>

                <div class="pricing-card enterprise">
                    <div class="pricing-header">
                        <h3 class="pricing-title">企业版</h3>
                        <div class="pricing-price">
                            <span class="price-amount">定制</span>
                        </div>
                        <p class="pricing-description">为大型组织量身定制</p>
                    </div>
                    <ul class="pricing-features">
                        <li>所有模型和功能</li>
                        <li>API 访问权限</li>
                        <li>专属客户经理</li>
                        <li>安全合规保障</li>
                        <li>24/7 技术支持</li>
                    </ul>
                    <a href="#contact" class="pricing-button">联系销售</a>
                </div>
            </div>
        </div>
    </section>

    <!-- CTA Section -->
    <section id="start" class="cta-section">
        <div class="container">
            <div class="cta-content">
                <h2 class="cta-title">开始使用 Claude 4</h2>
                <p class="cta-subtitle">体验下一代AI助手的强大能力</p>
                <div class="cta-buttons">
                    <a href="https://claude.ai" class="btn btn-primary large">免费试用</a>
                    <a href="https://docs.anthropic.com" class="btn btn-secondary large">查看文档</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4 class="footer-title">产品</h4>
                    <ul class="footer-links">
                        <li><a href="#">Claude Opus 4</a></li>
                        <li><a href="#">Claude Sonnet 4</a></li>
                        <li><a href="#">Claude Code</a></li>
                        <li><a href="#">API 平台</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 class="footer-title">解决方案</h4>
                    <ul class="footer-links">
                        <li><a href="#">AI 代理</a></li>
                        <li><a href="#">编程助手</a></li>
                        <li><a href="#">客户支持</a></li>
                        <li><a href="#">内容创作</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 class="footer-title">资源</h4>
                    <ul class="footer-links">
                        <li><a href="#">文档</a></li>
                        <li><a href="#">API 参考</a></li>
                        <li><a href="#">社区</a></li>
                        <li><a href="#">博客</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4 class="footer-title">公司</h4>
                    <ul class="footer-links">
                        <li><a href="#">关于我们</a></li>
                        <li><a href="#">职业机会</a></li>
                        <li><a href="#">新闻</a></li>
                        <li><a href="#">联系我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2025 Anthropic. 保留所有权利。</p>
                <div class="footer-legal">
                    <a href="#">隐私政策</a>
                    <a href="#">服务条款</a>
                    <a href="#">使用政策</a>
                </div>
            </div>
        </div>
    </footer>

    <script src="script.js"></script>
</body>
</html>
