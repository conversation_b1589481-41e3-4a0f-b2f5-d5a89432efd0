<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 - 播客App</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="mobile-container">
        <!-- iOS 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span>9:41</span>
            </div>
            <div class="status-right">
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi"></i>
                <span>100%</span>
                <i class="fas fa-battery-full"></i>
            </div>
        </div>

        <!-- 导航栏 -->
        <div class="nav-bar">
            <div class="nav-title">首页</div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 搜索栏 -->
            <div class="px-4 py-3">
                <div class="relative">
                    <input type="text" placeholder="搜索播客、单集或主播" 
                           class="w-full bg-white rounded-lg px-4 py-3 pl-10 text-sm border border-gray-200 focus:outline-none focus:border-blue-500">
                    <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                </div>
            </div>

            <!-- 最近播放 -->
            <div class="px-4 mb-6">
                <h2 class="text-lg font-semibold mb-3 text-gray-900">最近播放</h2>
                <div class="bg-white rounded-xl p-4 shadow-sm">
                    <div class="flex items-center space-x-3">
                        <img src="https://images.unsplash.com/photo-1478737270239-2f02b77fc618?w=60&h=60&fit=crop&crop=center" 
                             alt="播客封面" class="w-15 h-15 rounded-lg">
                        <div class="flex-1">
                            <h3 class="font-medium text-gray-900 text-sm">科技聊天室</h3>
                            <p class="text-xs text-gray-500 mt-1">第42期：AI的未来发展趋势</p>
                            <div class="flex items-center mt-2">
                                <div class="progress-bar flex-1 mr-3">
                                    <div class="progress-fill" style="width: 35%"></div>
                                </div>
                                <span class="text-xs text-gray-400">12:30</span>
                            </div>
                        </div>
                        <button class="w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center">
                            <i class="fas fa-play text-white text-sm"></i>
                        </button>
                    </div>
                </div>
            </div>

            <!-- 推荐播客 -->
            <div class="px-4 mb-6">
                <div class="flex items-center justify-between mb-3">
                    <h2 class="text-lg font-semibold text-gray-900">为你推荐</h2>
                    <button class="text-blue-500 text-sm">查看全部</button>
                </div>
                <div class="space-y-3">
                    <!-- 推荐项目1 -->
                    <div class="bg-white rounded-xl p-4 shadow-sm">
                        <div class="flex items-start space-x-3">
                            <img src="https://images.unsplash.com/photo-1590602847861-f357a9332bbc?w=60&h=60&fit=crop&crop=center" 
                                 alt="播客封面" class="w-15 h-15 rounded-lg">
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-900 text-sm">创业故事汇</h3>
                                <p class="text-xs text-gray-500 mt-1">分享创业路上的酸甜苦辣</p>
                                <div class="flex items-center mt-2 space-x-4">
                                    <span class="text-xs text-gray-400">
                                        <i class="fas fa-headphones mr-1"></i>12.5万
                                    </span>
                                    <span class="text-xs text-gray-400">
                                        <i class="fas fa-star mr-1"></i>4.8
                                    </span>
                                </div>
                            </div>
                            <button class="btn-secondary text-xs px-3 py-1">订阅</button>
                        </div>
                    </div>

                    <!-- 推荐项目2 -->
                    <div class="bg-white rounded-xl p-4 shadow-sm">
                        <div class="flex items-start space-x-3">
                            <img src="https://images.unsplash.com/photo-1516280440614-37939bbacd81?w=60&h=60&fit=crop&crop=center" 
                                 alt="播客封面" class="w-15 h-15 rounded-lg">
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-900 text-sm">深度思考</h3>
                                <p class="text-xs text-gray-500 mt-1">探讨人生哲学与社会现象</p>
                                <div class="flex items-center mt-2 space-x-4">
                                    <span class="text-xs text-gray-400">
                                        <i class="fas fa-headphones mr-1"></i>8.3万
                                    </span>
                                    <span class="text-xs text-gray-400">
                                        <i class="fas fa-star mr-1"></i>4.6
                                    </span>
                                </div>
                            </div>
                            <button class="btn-secondary text-xs px-3 py-1">订阅</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 热门单集 -->
            <div class="px-4 mb-6">
                <div class="flex items-center justify-between mb-3">
                    <h2 class="text-lg font-semibold text-gray-900">热门单集</h2>
                    <button class="text-blue-500 text-sm">查看全部</button>
                </div>
                <div class="space-y-3">
                    <!-- 热门单集1 -->
                    <div class="bg-white rounded-xl p-4 shadow-sm">
                        <div class="flex items-start space-x-3">
                            <img src="https://images.unsplash.com/photo-1493225457124-a3eb161ffa5f?w=60&h=60&fit=crop&crop=center" 
                                 alt="播客封面" class="w-15 h-15 rounded-lg">
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-900 text-sm">如何在30岁前实现财务自由</h3>
                                <p class="text-xs text-gray-500 mt-1">理财有道 · 45分钟</p>
                                <div class="flex items-center mt-2 space-x-4">
                                    <span class="text-xs text-gray-400">
                                        <i class="fas fa-play mr-1"></i>15.2万
                                    </span>
                                    <span class="text-xs text-gray-400">
                                        <i class="fas fa-heart mr-1"></i>3.2万
                                    </span>
                                </div>
                            </div>
                            <button class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-play text-white text-xs"></i>
                            </button>
                        </div>
                    </div>

                    <!-- 热门单集2 -->
                    <div class="bg-white rounded-xl p-4 shadow-sm">
                        <div class="flex items-start space-x-3">
                            <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=60&h=60&fit=crop&crop=center" 
                                 alt="播客封面" class="w-15 h-15 rounded-lg">
                            <div class="flex-1">
                                <h3 class="font-medium text-gray-900 text-sm">职场沟通的艺术</h3>
                                <p class="text-xs text-gray-500 mt-1">职场进阶 · 38分钟</p>
                                <div class="flex items-center mt-2 space-x-4">
                                    <span class="text-xs text-gray-400">
                                        <i class="fas fa-play mr-1"></i>9.8万
                                    </span>
                                    <span class="text-xs text-gray-400">
                                        <i class="fas fa-heart mr-1"></i>2.1万
                                    </span>
                                </div>
                            </div>
                            <button class="w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center">
                                <i class="fas fa-play text-white text-xs"></i>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部标签栏 -->
        <div class="tab-bar">
            <div class="tab-item active">
                <i class="fas fa-home"></i>
                <span>首页</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-compass"></i>
                <span>发现</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-bookmark"></i>
                <span>订阅</span>
            </div>
            <div class="tab-item">
                <i class="fas fa-user"></i>
                <span>我的</span>
            </div>
        </div>
    </div>
</body>
</html>
