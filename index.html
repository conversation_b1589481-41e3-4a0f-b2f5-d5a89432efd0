<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>播客App原型 - 类似小宇宙</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-100 p-8">
    <div class="max-w-7xl mx-auto">
        <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">播客App高保真原型设计</h1>
        <p class="text-center text-gray-600 mb-12">类似小宇宙的播客应用 - iPhone 15 Pro 界面展示</p>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            <!-- 首页 -->
            <div class="prototype-container">
                <h3 class="prototype-title">首页 - 推荐与发现</h3>
                <div class="phone-frame">
                    <iframe src="home.html" class="phone-screen"></iframe>
                </div>
            </div>

            <!-- 发现页 -->
            <div class="prototype-container">
                <h3 class="prototype-title">发现页 - 分类浏览</h3>
                <div class="phone-frame">
                    <iframe src="discover.html" class="phone-screen"></iframe>
                </div>
            </div>

            <!-- 播放页 -->
            <div class="prototype-container">
                <h3 class="prototype-title">播放页 - 音频控制</h3>
                <div class="phone-frame">
                    <iframe src="player.html" class="phone-screen"></iframe>
                </div>
            </div>

            <!-- 订阅页 -->
            <div class="prototype-container">
                <h3 class="prototype-title">订阅页 - 我的播客</h3>
                <div class="phone-frame">
                    <iframe src="subscriptions.html" class="phone-screen"></iframe>
                </div>
            </div>

            <!-- 个人页 -->
            <div class="prototype-container">
                <h3 class="prototype-title">个人页 - 用户中心</h3>
                <div class="phone-frame">
                    <iframe src="profile.html" class="phone-screen"></iframe>
                </div>
            </div>
        </div>

        <div class="mt-16 text-center">
            <h2 class="text-2xl font-bold mb-4 text-gray-800">设计说明</h2>
            <div class="bg-white rounded-lg p-6 shadow-lg max-w-4xl mx-auto">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-left">
                    <div>
                        <h4 class="font-semibold text-lg mb-2">设计特点</h4>
                        <ul class="text-gray-600 space-y-1">
                            <li>• iPhone 15 Pro 尺寸比例 (393×852px)</li>
                            <li>• iOS 设计规范与圆角处理</li>
                            <li>• 真实图片素材，非占位符</li>
                            <li>• 完整的状态栏与导航栏</li>
                            <li>• 现代化的UI组件与图标</li>
                        </ul>
                    </div>
                    <div>
                        <h4 class="font-semibold text-lg mb-2">技术实现</h4>
                        <ul class="text-gray-600 space-y-1">
                            <li>• Tailwind CSS 响应式布局</li>
                            <li>• FontAwesome 图标库</li>
                            <li>• 模块化HTML文件结构</li>
                            <li>• iframe 嵌入式展示</li>
                            <li>• 移动优先设计理念</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
